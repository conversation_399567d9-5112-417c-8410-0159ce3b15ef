<?php

namespace Modules\TelegramStarPayment\Listeners;

use Modules\TelegramBot\Events\TelegramPaymentSuccessful;
use Illuminate\Support\Facades\Log;
use Modules\TelegramStarPayment\Models\TelegramTransaction;
use Modules\TelegramStarPayment\app\Events\AfterStarPaymentSucceeded;

class PaymentSuccessfulListener
{

    /**
     * Handle the event.
     */
    public function handle(TelegramPaymentSuccessful $event): void
    {
        try {
            $message = $event->webhookData['message'];
            $payment = $message['successful_payment'];

            $this->processSuccessfulPayment($payment);
        } catch (\Exception $e) {
            Log::error('Error processing successful payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'webhook_data' => $event->webhookData,
            ]);

            throw $e;
        }
    }

    /**
     * Process the successful payment
     */
    private function processSuccessfulPayment(array $payment): void
    {
        $payload = $payment['invoice_payload'] ?? null;
        if ($payload) {
            $payload = json_decode($payload, true);
        }

        $transHash = $payload['transaction_hash'];
        $exist = TelegramTransaction::where('transaction_hash', $transHash)->exists();

        if (!$exist) {
            $transaction = TelegramTransaction::create([
                'currency' => $payment['currency'],
                'total_amount' => $payment['total_amount'],
                'invoice_payload' => $payment['invoice_payload'],
                'telegram_payment_charge_id' => $payment['telegram_payment_charge_id'],
                'provider_payment_charge_id' => $payment['provider_payment_charge_id'],
                'transaction_hash' => $payload['transaction_hash'],
                'user_id' => $payload['user_id'],
            ]);
        }

        event(new AfterStarPaymentSucceeded($transaction->id));
    }
}
