<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\Blocked;
use App\Models\User;
use App\Services\ReferralService;
use App\Services\SettingsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    protected ReferralService $referralService;
    protected SettingsService $settingsService;

    public function __construct(ReferralService $referralService, SettingsService $settingsService)
    {
        $this->referralService = $referralService;
        $this->settingsService = $settingsService;
    }

    /**
     * Sync user data - handles both INIT User and Restored User scenarios
     * If user exists (by tele_id): update last_active only and return new token
     * If user doesn't exist: create new user with provided data + last_active and return token
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'telegram_id' => 'required|string',
            'name' => 'required|string|max:255',
            'country' => 'nullable|string|max:255',
            'language_code' => 'nullable|string|max:10',
            'username' => 'nullable|string|max:255',
            'avatar_url' => 'nullable|string|max:255',
            'referral_code' => 'nullable|string|max:8',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $telegramId = $request->telegram_id;
        $deviceName = $request->device_name ?? 'mobile-app';
        $now = Carbon::now();

        // Check if user exists by tele_id
        $user = User::where('tele_id', $telegramId)->first();

        $settings = $this->settingsService->getSettingsByKeys(['base_speed']);
        $baseSpeed = $settings['base_speed'];
        if (empty($baseSpeed)) {
            throw new \Exception('Base speed not set');
        }

        if ($user) {
            $user->username = $request->username;
            $user->avatar_url = $request->avatar_url;
            $user->last_active = $now;

            if (empty((int) $user->speed)) {
                $user->speed = $baseSpeed;
            }

            if (empty($user->last_checkpoint)) {
                $user->last_checkpoint = $now;
            }

            $user->save();

            // Revoke previous tokens for this device and create new one
            $user->tokens()->where('name', $telegramId)->delete();
            $token = $user->createToken($telegramId, ['*'], now()->addDays(30));

            return response()->json([
                'success' => true,
                'message' => 'User restored successfully',
                'data' => [
                    'user' => new UserResource($user),
                    'token' => $token->plainTextToken,
                    'token_expires_at' => $token->accessToken->expires_at,
                    'action' => 'restored',
                ],
            ], 200);
        } else {
            // User doesn't exist - INIT User scenario
            // Create new user with provided data

            if (! $settings['base_speed']) {
                throw new \Exception('Base speed not found');
            }

            $user = User::create([
                'tele_id' => $telegramId,
                'name' => $request->name,
                'username' => $request->username,
                'country' => $request->country,
                'language_code' => $request->language_code,
                'last_active' => $now,
                'balance_ton' => 0.00000000, // Default balance
                'balance_token' => 0.00000000, // Default balance
                'speed' => $baseSpeed,
                'pending_token' => 0.00000000, // Default pending token
                'last_checkpoint' => $now, // Initialize with current time,
                'avatar_url' => $request->avatar_url,
            ]);

            // Generate referral code for new user
            $user->generateReferralCode();

            // Handle referral relationship if referral code provided
            $referralInfo = null;
            if ($request->filled('referral_code')) {
                $referralInfo = $this->referralService->handleReferral($user, $request->referral_code);
            }

            // Create token for the new user
            $token = $user->createToken($deviceName, ['*'], now()->addDays(30));

            $responseData = [
                'user' => new UserResource($user),
                'token' => $token->plainTextToken,
                'token_expires_at' => $token->accessToken->expires_at,
                'action' => 'initialized',
            ];

            // Add referral information if available
            if ($referralInfo) {
                $responseData['referral'] = $referralInfo;
            }

            return response()->json([
                'success' => true,
                'message' => 'User initialized successfully',
                'data' => $responseData,
            ], 201);
        }
    }

    /**
     * Logout user (revoke current token)
     */
    public function logout(Request $request)
    {
        // Revoke current token
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully',
        ], 200);
    }

    /**
     * Get a paginated list of users.
     *
     * This endpoint is for administrators to retrieve a list of all users
     * in the system. The list is paginated (25 per page) and ordered by the
     * user's creation date in descending order (newest users first).
     *
     * It also allows searching for users by their name (partial match).
     *
     * @return \Illuminate\Http\JsonResponse A paginated list of users.
     */
    public function list(Request $request)
    {
        $query = User::query();
        $q = $request->input('q');

        if ($q) {
            $query->where('name', 'like', '%'.$q.'%');
        }

        $users = $query->orderBy('created_at', 'desc')
            ->paginate(25);

        return response()->json([
            'success' => true,
            'message' => 'Users list retrieved successfully',
            'data' => $users,
        ], 200);
    }

    /**
     * Block a user - only accessible by admin
     */
    public function blockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $userId = $request->user_id;
        $reason = $request->reason;

        // Get staff ID from request attributes (set by AdminAuth middleware)
        $staffId = $request->attributes->get('staff_id', 1); // Default to 1 if not set

        // Check if user is already blocked
        $existingBlock = Blocked::where('user_id', $userId)->first();

        if ($existingBlock) {
            return response()->json([
                'success' => false,
                'message' => 'User is already blocked',
                'error_code' => 'USER_ALREADY_BLOCKED',
                'data' => [
                    'user_id' => $userId,
                    'current_reason' => $existingBlock->reason,
                    'blocked_at' => $existingBlock->created_at,
                    'blocked_by_staff_id' => $existingBlock->staff_id,
                ],
            ], 409);
        }

        // Get user info for response
        $user = User::find($userId);

        if (! $user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found',
                'error_code' => 'USER_NOT_FOUND',
            ], 404);
        }

        // Create block record
        $blocked = Blocked::create([
            'user_id' => $userId,
            'staff_id' => $staffId,
            'reason' => $reason,
        ]);

        // Revoke all user tokens to immediately block access
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'User blocked successfully',
            'data' => [
                'block_id' => $blocked->id,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'tele_id' => $user->tele_id,
                    'username' => $user->username,
                ],
                'reason' => $blocked->reason,
                'blocked_at' => $blocked->created_at,
                'blocked_by_staff_id' => $staffId,
                'tokens_revoked' => true,
            ],
        ], 201);
    }

    /**
     * Unblock a user - only accessible by admin
     */
    public function unblockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $userId = $request->user_id;

        // Get staff ID from request attributes (set by AdminAuth middleware)
        $staffId = $request->attributes->get('staff_id', 1); // Default to 1 if not set

        // Find the block record
        $blocked = Blocked::where('user_id', $userId)->first();

        if (! $blocked) {
            return response()->json([
                'success' => false,
                'message' => 'User is not blocked',
                'error_code' => 'USER_NOT_BLOCKED',
                'data' => [
                    'user_id' => $userId,
                ],
            ], 404);
        }

        // Get user info for response
        $user = User::find($userId);

        if (! $user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found',
                'error_code' => 'USER_NOT_FOUND',
            ], 404);
        }

        // Store block info before deletion
        $blockInfo = [
            'block_id' => $blocked->id,
            'original_reason' => $blocked->reason,
            'blocked_at' => $blocked->created_at,
            'blocked_by_staff_id' => $blocked->staff_id,
        ];

        // Delete the block record
        $blocked->delete();

        return response()->json([
            'success' => true,
            'message' => 'User unblocked successfully',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'tele_id' => $user->tele_id,
                    'username' => $user->username,
                ],
                'unblocked_at' => now(),
                'unblocked_by_staff_id' => $staffId,
                'previous_block' => $blockInfo,
            ],
        ], 200);
    }

    /**
     * Get blocked users list - only accessible by admin
     */
    public function getBlockedUsers(Request $request)
    {
        $blockedUsers = Blocked::with(['user', 'staff'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'message' => 'Blocked users retrieved successfully',
            'data' => [
                'blocked_users' => $blockedUsers->items(),
                'pagination' => [
                    'current_page' => $blockedUsers->currentPage(),
                    'total_pages' => $blockedUsers->lastPage(),
                    'total_items' => $blockedUsers->total(),
                    'per_page' => $blockedUsers->perPage(),
                ],
            ],
        ], 200);
    }

    /**
     * Get current authenticated user's information
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
                'error_code' => 'USER_NOT_AUTHENTICATED',
            ], 401);
        }

        // Update last active timestamp
        $user->updateLastSeen();

        return response()->json([
            'success' => true,
            'message' => 'User information retrieved successfully',
            'data' => [
                'user' => new UserResource($user),
            ],
        ], 200);
    }
}
