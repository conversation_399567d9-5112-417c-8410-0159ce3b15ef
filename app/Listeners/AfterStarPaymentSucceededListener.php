<?php

namespace App\Listeners;

use App\Helpers\TonHelper;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Transaction;
use Modules\TelegramStarPayment\Events\AfterStarPaymentSucceeded;
use App\Models\User;

class AfterStarPaymentSucceededListener
{
    public function handle(AfterStarPaymentSucceeded $event)
    {
        $transaction = $event->transaction;

        $user = User::find($transaction->user_id);
        $starAmount = $transaction->amount;

        $tonHelper = app(TonHelper::class);
        $nanoTon = $tonHelper->convertStarToNanoTon($starAmount);

        $transaction = Transaction::create([
            'user_id' => $user->id,
            'value' => $starAmount,
            'type' => TransactionType::TELEGRAM_STARS_PAYMENT,
            'status' => TransactionStatus::COMPLETED,
            'description' => trans('transactions.transfer_star_to_ton', ['stars' => $starAmount, 'ton' => number_format($nanoTon / 10 ** 9, 6)]),
            'hash' => Transaction::generateHash(),
        ]);

        $user->addTonBalance($nanoTon / 10 ** 9);
    }
}
